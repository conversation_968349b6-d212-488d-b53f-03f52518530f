@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- <PERSON> Header -->
    <x-page-header
        title="Assignment Management"
        description="Manage assignments and coursework"
        :back-route="route('admin.dashboard')"
        back-label="Back to Dashboard">
        <a href="{{ route('admin.grading.assignments.create') }}" class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Create Assignment
        </a>
    </x-page-header>

    <!-- Statistics Cards -->
    <div class="grid-stats-5">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="stat-card-icon bg-blue-100">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="stat-card-value">{{ $stats['total'] }}</p>
                        <p class="stat-card-label">Total Assignments</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="stat-card-icon bg-green-100">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="stat-card-value">{{ $stats['active'] }}</p>
                        <p class="stat-card-label">Active</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="stat-card-icon bg-yellow-100">
                        <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="stat-card-value">{{ $stats['draft'] }}</p>
                        <p class="stat-card-label">Draft</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="stat-card-icon bg-purple-100">
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="stat-card-value">{{ $stats['completed'] }}</p>
                        <p class="stat-card-label">Completed</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="stat-card-icon bg-red-100">
                        <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="stat-card-value">{{ $stats['overdue'] }}</p>
                        <p class="stat-card-label">Overdue</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg p-6" x-data="{
        searchQuery: '',
        academicYear: '',
        academicTerm: '',
        category: '',
        subject: '',
        status: '',
        showAdvanced: false
    }">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            <!-- Search Input -->
            <div class="flex-1">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input
                        type="text"
                        x-model="searchQuery"
                        @input="filterAssignments()"
                        class="search-input"
                        placeholder="Search assignments by title, code, description..."
                    >
                </div>
            </div>

            <!-- Filter Toggle and Clear Buttons -->
            <div class="flex items-center space-x-3">
                <button
                    @click="showAdvanced = !showAdvanced"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                    </svg>
                    Filters
                    <svg class="w-4 h-4 ml-1 transition-transform duration-200" :class="showAdvanced ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>

                <!-- Clear All Filters -->
                <button
                    @click="clearAssignmentFilters()"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Clear All Filters
                </button>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div x-show="showAdvanced" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 transform translate-y-0" x-transition:leave-end="opacity-0 transform -translate-y-2" class="mt-4 pt-4 border-t border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <!-- Academic Year Filter -->
                <div>
                    <label for="year-filter" class="block text-sm font-medium text-gray-700 mb-1">Academic Year</label>
                    <select
                        id="year-filter"
                        x-model="academicYear"
                        @change="filterAssignments()"
                        class="form-select"
                    >
                        <option value="">All Years</option>
                        @foreach($academicYears as $year)
                            <option value="{{ $year->id }}">{{ $year->name }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Academic Term Filter -->
                <div>
                    <label for="term-filter" class="block text-sm font-medium text-gray-700 mb-1">Academic Term</label>
                    <select
                        id="term-filter"
                        x-model="academicTerm"
                        @change="filterAssignments()"
                        class="form-select"
                    >
                        <option value="">All Terms</option>
                        @foreach($academicTerms as $term)
                            <option value="{{ $term->id }}">{{ $term->name }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Category Filter -->
                <div>
                    <label for="category-filter" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    <select
                        id="category-filter"
                        x-model="category"
                        @change="filterAssignments()"
                        class="form-select"
                    >
                        <option value="">All Categories</option>
                        @foreach($gradeCategories as $gradeCategory)
                            <option value="{{ $gradeCategory->id }}">{{ $gradeCategory->name }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Subject Filter -->
                <div>
                    <label for="subject-filter" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                    <select
                        id="subject-filter"
                        x-model="subject"
                        @change="filterAssignments()"
                        class="form-select"
                    >
                        <option value="">All Subjects</option>
                        @foreach($subjects as $subjectItem)
                            <option value="{{ $subjectItem->id }}">{{ $subjectItem->name }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select
                        id="status-filter"
                        x-model="status"
                        @change="filterAssignments()"
                        class="form-select"
                    >
                        <option value="">All Status</option>
                        <option value="draft">Draft</option>
                        <option value="assigned">Assigned</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Assignments List -->
    <div class="card">
        @if($assignments->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assignment</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject & Class</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Marks</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($assignments as $assignment)
                            <tr class="hover:bg-gray-50 assignment-row"
                                data-filterable
                                data-search-text="{{ strtolower($assignment->title . ' ' . $assignment->assignment_code . ' ' . ($assignment->description ?? '')) }}"
                                data-academic-year="{{ $assignment->academic_year_id }}"
                                data-academic-term="{{ $assignment->academic_term_id }}"
                                data-category="{{ $assignment->grade_category_id }}"
                                data-subject="{{ $assignment->subject_id }}"
                                data-status="{{ $assignment->status }}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $assignment->title }}</div>
                                        <div class="text-sm text-gray-500">{{ $assignment->assignment_code }}</div>
                                        <div class="text-xs text-gray-400">
                                            {{ $assignment->academicYear->name }} - {{ $assignment->academicTerm->name }}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="badge {{ $assignment->gradeCategory->badge_color }}">
                                        {{ $assignment->gradeCategory->name }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $assignment->subject->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $assignment->classRoom->name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $assignment->formatted_due_date }}</div>
                                    @if($assignment->is_overdue)
                                        <span class="badge badge-red">Overdue</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">Total: {{ number_format($assignment->total_marks, 1) }}</div>
                                    <div class="text-sm text-gray-500">Pass: {{ number_format($assignment->passing_marks, 1) }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="badge {{ $assignment->status_badge_color }}">
                                        {{ ucfirst($assignment->status) }}
                                    </span>
                                    @if($assignment->is_published)
                                        <span class="badge badge-green ml-1">Published</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex items-center justify-end space-x-2">
                                        <!-- Primary Actions (Always Visible) -->
                                        <a href="{{ route('admin.grading.assignments.show', $assignment) }}"
                                           class="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-md transition-colors duration-200">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            View
                                        </a>

                                        <a href="{{ route('admin.grading.assignments.edit', $assignment) }}"
                                           class="inline-flex items-center px-3 py-1.5 bg-indigo-600 hover:bg-indigo-700 text-white text-xs font-medium rounded-md transition-colors duration-200">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                            Edit
                                        </a>

                                        <!-- Dropdown Menu -->
                                        <div class="relative inline-block text-left">
                                            <button type="button"
                                                    onclick="toggleTableDropdown(this)"
                                                    class="inline-flex items-center justify-center w-8 h-8 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-md transition-colors duration-200"
                                                    title="More actions">
                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                                                </svg>
                                            </button>

                                            <!-- Dropdown menu (hidden by default) -->
                                            <div class="table-dropdown-menu absolute right-0 mt-1 w-44 bg-white rounded-md shadow-lg border border-gray-200 z-50 hidden">
                                                <div class="py-1">
                                                    <button type="button"
                                                            onclick="toggleAssignmentPublish('{{ $assignment->id }}', '{{ $assignment->title }}', {{ $assignment->is_published ? 'false' : 'true' }})"
                                                            class="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200">
                                                        @if($assignment->is_published)
                                                            <svg class="w-4 h-4 mr-2 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                                            </svg>
                                                            Unpublish
                                                        @else
                                                            <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                            </svg>
                                                            Publish
                                                        @endif
                                                    </button>

                                                    <button type="button"
                                                            onclick="duplicateAssignment('{{ $assignment->id }}', '{{ $assignment->title }}')"
                                                            class="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200">
                                                        <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                        </svg>
                                                        Duplicate
                                                    </button>

                                                    @if($assignment->status === 'draft')
                                                        <button type="button"
                                                                onclick="deleteAssignment('{{ $assignment->id }}', '{{ $assignment->title }}')"
                                                                class="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200">
                                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                            </svg>
                                                            Delete
                                                        </button>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="px-6 py-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No assignments</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating your first assignment.</p>
                <div class="mt-6">
                    <a href="{{ route('admin.grading.assignments.create') }}" class="btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Create Assignment
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
// Live filtering functions for assignments
function filterAssignments() {
    const searchQuery = document.querySelector('[x-model="searchQuery"]').value.toLowerCase();
    const academicYear = document.querySelector('[x-model="academicYear"]').value;
    const academicTerm = document.querySelector('[x-model="academicTerm"]').value;
    const category = document.querySelector('[x-model="category"]').value;
    const subject = document.querySelector('[x-model="subject"]').value;
    const status = document.querySelector('[x-model="status"]').value;

    const rows = document.querySelectorAll('[data-filterable]');
    let visibleCount = 0;

    rows.forEach(row => {
        const searchText = row.getAttribute('data-search-text').toLowerCase();
        const rowYear = row.getAttribute('data-academic-year');
        const rowTerm = row.getAttribute('data-academic-term');
        const rowCategory = row.getAttribute('data-category');
        const rowSubject = row.getAttribute('data-subject');
        const rowStatus = row.getAttribute('data-status');

        let isVisible = true;

        // Search filter
        if (searchQuery && !searchText.includes(searchQuery)) {
            isVisible = false;
        }

        // Academic Year filter
        if (academicYear && rowYear !== academicYear) {
            isVisible = false;
        }

        // Academic Term filter
        if (academicTerm && rowTerm !== academicTerm) {
            isVisible = false;
        }

        // Category filter
        if (category && rowCategory !== category) {
            isVisible = false;
        }

        // Subject filter
        if (subject && rowSubject !== subject) {
            isVisible = false;
        }

        // Status filter
        if (status && rowStatus !== status) {
            isVisible = false;
        }

        // Show/hide row
        row.style.display = isVisible ? '' : 'none';
        if (isVisible) visibleCount++;
    });

    // Update results count if element exists
    const resultsCount = document.querySelector('[data-results-count]');
    if (resultsCount) {
        resultsCount.textContent = visibleCount;
    }
}

// Clear all filters
function clearAssignmentFilters() {
    // Reset form inputs and trigger events
    const searchInput = document.querySelector('[x-model="searchQuery"]');
    const yearInput = document.querySelector('[x-model="academicYear"]');
    const termInput = document.querySelector('[x-model="academicTerm"]');
    const categoryInput = document.querySelector('[x-model="category"]');
    const subjectInput = document.querySelector('[x-model="subject"]');
    const statusInput = document.querySelector('[x-model="status"]');

    if (searchInput) {
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
    }
    if (yearInput) {
        yearInput.value = '';
        yearInput.dispatchEvent(new Event('change'));
    }
    if (termInput) {
        termInput.value = '';
        termInput.dispatchEvent(new Event('change'));
    }
    if (categoryInput) {
        categoryInput.value = '';
        categoryInput.dispatchEvent(new Event('change'));
    }
    if (subjectInput) {
        subjectInput.value = '';
        subjectInput.dispatchEvent(new Event('change'));
    }
    if (statusInput) {
        statusInput.value = '';
        statusInput.dispatchEvent(new Event('change'));
    }

    // Show all rows
    const rows = document.querySelectorAll('[data-filterable]');
    rows.forEach(row => {
        row.style.display = '';
    });

    // Update results count if element exists
    const resultsCount = document.querySelector('[data-results-count]');
    if (resultsCount) {
        resultsCount.textContent = rows.length;
    }
}

// Table dropdown functionality
function toggleTableDropdown(button) {
    const dropdown = button.nextElementSibling;
    const allDropdowns = document.querySelectorAll('.table-dropdown-menu');

    // Close all other dropdowns
    allDropdowns.forEach(menu => {
        if (menu !== dropdown) {
            menu.classList.add('hidden');
        }
    });

    // Toggle current dropdown
    dropdown.classList.toggle('hidden');
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('.relative')) {
        const allDropdowns = document.querySelectorAll('.table-dropdown-menu');
        allDropdowns.forEach(menu => {
            menu.classList.add('hidden');
        });
    }
});

// Toggle assignment publish status
async function toggleAssignmentPublish(assignmentId, assignmentTitle, newStatus) {
    const action = newStatus === 'true' ? 'publish' : 'unpublish';

    const confirmed = await confirmAction(
        `${action.charAt(0).toUpperCase() + action.slice(1)} Assignment`,
        `Are you sure you want to ${action} "${assignmentTitle}"?`
    );

    if (confirmed) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{{ route('admin.grading.assignments.index') }}/${assignmentId}/toggle-published`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

// Duplicate assignment
async function duplicateAssignment(assignmentId, assignmentTitle) {
    const confirmed = await confirmAction(
        'Duplicate Assignment',
        `Are you sure you want to duplicate "${assignmentTitle}"?`
    );

    if (confirmed) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{{ route('admin.grading.assignments.index') }}/${assignmentId}/duplicate`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

// Delete assignment
async function deleteAssignment(assignmentId, assignmentTitle) {
    const confirmed = await confirmDelete(`assignment "${assignmentTitle}"`);

    if (confirmed) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/grading/assignments/${assignmentId}`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';

        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
